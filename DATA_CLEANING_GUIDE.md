# 数据清理工具使用指南

本项目提供了两个数据清理脚本，用于清理data目录中数据残缺的CSV文件。

## 🛠️ 清理工具

### 1. 快速清理工具 (`quick_clean.py`)

**适用场景**: 清理明显有问题的文件
**清理标准**:
- 文件大小 < 1KB
- 数据行数 < 10行
- 缺少必要列 (date, open, high, low, close, volume)
- 所有价格数据为空
- 价格数据空值比例 > 80%

**使用方法**:
```bash
# 模拟运行（查看将要删除的文件）
python quick_clean.py --dry-run

# 实际清理
python quick_clean.py

# 不备份直接删除
python quick_clean.py --no-backup

# 自定义备份目录
python quick_clean.py --backup-dir ./my_backup
```

### 2. 严格清理工具 (`strict_clean.py`)

**适用场景**: 更严格的数据质量要求
**清理标准**:
- 文件大小 < 2KB
- 数据行数 < 100行
- 缺少必要列
- 所有价格数据为空
- 价格数据空值比例 > 30%
- 有效价格数据比例 < 70%

**使用方法**:
```bash
# 模拟运行
python strict_clean.py --dry-run

# 实际清理
python strict_clean.py

# 自定义清理标准
python strict_clean.py --max-null-ratio 0.2 --min-rows 200 --min-size 4096
```

## 📊 清理结果统计

### 第一轮清理 (quick_clean.py)
- **总文件数**: 1915
- **问题文件数**: 1855
- **删除文件数**: 1855
- **主要问题**: 文件过小(70-130字节)、价格数据空值比例过高(80%+)

### 第二轮清理 (strict_clean.py)
- **总文件数**: 60
- **问题文件数**: 14
- **删除文件数**: 14
- **主要问题**: 价格数据空值比例超过30%

### 最终结果
- **保留文件数**: 46个高质量CSV文件
- **清理率**: 97.6% (1869/1915)
- **备份位置**: 
  - `data_backup/` (第一轮清理的备份)
  - `data_backup_strict/` (第二轮清理的备份)

## 🔍 数据质量检查

清理后的文件具备以下特征:
- ✅ 文件大小 ≥ 2KB
- ✅ 数据行数 ≥ 100行
- ✅ 包含完整的必要列
- ✅ 价格数据空值比例 ≤ 30%
- ✅ 有效价格数据比例 ≥ 70%

## 📁 文件结构

```
data/
├── 000011.csv          # 高质量数据文件
├── 000019.csv
├── ...                 # 其他46个高质量文件
├── mktcap_20250715.csv # 市值数据文件
├── quick_clean_report_20250715_144238.txt
└── strict_clean_report_20250715_144458.txt

data_backup/            # 第一轮清理备份
├── 000001.csv         # 被清理的问题文件
├── 000002.csv
└── ...                 # 1855个问题文件

data_backup_strict/     # 第二轮清理备份
├── 000006.csv         # 被严格清理的文件
├── 000016.csv
└── ...                 # 14个问题文件
```

## 🚀 使用建议

### 日常维护
1. **定期检查**: 建议每次数据抓取后运行快速清理
2. **质量保证**: 对重要分析使用严格清理标准
3. **备份管理**: 定期清理备份目录，避免占用过多空间

### 参数调优
```bash
# 更宽松的清理标准（保留更多文件）
python strict_clean.py --max-null-ratio 0.5 --min-rows 50

# 更严格的清理标准（更高质量要求）
python strict_clean.py --max-null-ratio 0.1 --min-rows 200 --min-size 5120
```

### 恢复数据
如果误删了重要文件，可以从备份目录恢复:
```bash
# 从快速清理备份恢复
cp data_backup/000001.csv data/

# 从严格清理备份恢复
cp data_backup_strict/000006.csv data/
```

## ⚠️ 注意事项

1. **备份重要性**: 默认会自动备份删除的文件，建议保留备份
2. **模拟运行**: 首次使用建议先用 `--dry-run` 查看效果
3. **批量处理**: 两个工具可以配合使用，先快速清理再严格清理
4. **数据验证**: 清理后建议抽查几个文件确认数据质量

## 📈 性能优化

清理后的数据具有以下优势:
- **存储效率**: 删除了97.6%的无效文件，节省存储空间
- **处理速度**: 减少了无效数据的读取和处理时间
- **分析准确性**: 确保分析基于高质量的完整数据
- **系统稳定性**: 避免因残缺数据导致的程序异常

## 🔧 自定义扩展

如需自定义清理逻辑，可以修改检查函数:
- `quick_check_file()` - 快速检查逻辑
- `strict_check_file()` - 严格检查逻辑

添加新的检查条件或调整现有标准以满足特定需求。
