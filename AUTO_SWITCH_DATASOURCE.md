# 自动切换数据源功能

## 功能概述

在开始数据抓取前，系统会预先检查本地文件状态：

- 如果文件不存在，自动切换到akshare数据源从1970年开始抓取完整历史数据
- 如果文件存在但数据行数少于指定阈值（默认50条），自动切换到akshare数据源重新抓取
- 如果文件存在且数据充足，使用原始配置的数据源

## 使用场景

1. **新股上市**：新上市的股票历史数据较少，通过切换数据源可以获得更完整的数据
2. **数据源限制**：某些数据源对特定股票的历史数据提供有限
3. **数据质量保证**：确保每只股票都有足够的历史数据用于技术分析

## 功能特点

- **XD股票检测**：自动识别近期除权股票（名称以XD开头），优先重新抓取完整历史数据
- **自动检测**：无需手动干预，系统自动检测数据行数
- **智能切换**：仅在数据不足时才切换，避免不必要的网络请求
- **完整历史**：切换后从1970年开始抓取，确保数据完整性
- **日志记录**：详细记录切换过程，便于监控和调试

## 触发条件

自动切换数据源功能按以下优先级触发：

1. **近期除权股票**（最高优先级）：股票名称以"XD"开头
2. **文件不存在**：本地CSV文件不存在时
3. **数据行数不足**：本地CSV文件存在但数据行数少于设定阈值时

当满足触发条件时，系统会：

- 自动切换到akshare数据源
- 从1970年1月1日开始抓取完整历史数据
- 强制使用非增量模式，确保数据完整性

## 参数说明

### 新增参数

```bash
--min-rows-threshold INT
```

- **默认值**：50
- **说明**：最小数据行数阈值
- **行为**：当获取的数据行数少于此值时，触发自动切换

### 使用示例

```bash
# 使用默认阈值（50行）
python fetch_kline.py --datasource mootdx

# 设置自定义阈值（100行）
python fetch_kline.py --datasource mootdx --min-rows-threshold 100

# 设置较低阈值（20行）
python fetch_kline.py --datasource mootdx --min-rows-threshold 20

# 禁用自动切换功能
python fetch_kline.py --datasource mootdx --min-rows-threshold 0
```

## 工作流程

1. **预检查阶段**：在数据抓取前检查本地文件状态
   - 检查CSV文件是否存在
   - 如果存在，读取并统计数据行数
   - 根据检查结果决定是否切换数据源

2. **数据源决策**：
   - 文件不存在 → 切换到akshare，从1970年开始
   - 文件存在但行数 < 阈值 → 切换到akshare，从1970年开始
   - 文件存在且行数 ≥ 阈值 → 使用原始数据源配置
   - 增量模式 → 跳过预检查，使用原始配置

3. **数据抓取**：使用决策后的数据源和日期范围抓取数据
4. **质量验证**：对获得的数据进行质量检查和保存

## 日志示例

```text
# XD股票检测（最高优先级）
2025-07-31 17:36:23,234 [INFO] 000001 检测到近期除权股票: XD平安银行
2025-07-31 17:36:23,236 [WARNING] 000001 检测到近期除权股票，切换到akshare数据源从1970年开始抓取

# 文件不存在时
2025-07-30 23:17:51,790 [INFO] 000001 本地文件不存在，切换到akshare数据源从1970年开始抓取

# 文件存在但数据不足时
2025-07-30 23:17:56,631 [WARNING] 000002 本地文件数据行数不足 (8 < 50)，切换到akshare数据源从1970年开始抓取

# 文件存在且数据充足时
2025-07-30 23:18:01,234 [DEBUG] 000003 本地文件数据充足 (65 行)，使用原始配置
```

## 技术实现

### 核心逻辑

```python
# 预检查：检查本地文件是否存在且数据行数是否足够
if csv_path.exists() and not incremental:
    try:
        existing_df = pd.read_csv(csv_path, parse_dates=["date"])
        if len(existing_df) < min_rows_threshold:
            logger.warning(
                "%s 本地文件数据行数不足 (%d < %d)，切换到akshare数据源从1970年开始抓取",
                code, len(existing_df), min_rows_threshold
            )
            current_datasource = "akshare"
            current_start = "19700101"
    except Exception as e:
        logger.warning("%s 读取本地文件失败: %s，切换到akshare数据源", code, e)
        current_datasource = "akshare"
        current_start = "19700101"
elif not csv_path.exists():
    # 文件不存在，直接切换到akshare获取完整历史数据
    logger.info("%s 本地文件不存在，切换到akshare数据源从1970年开始抓取", code)
    current_datasource = "akshare"
    current_start = "19700101"
```

### 关键变量

- `current_datasource`：当前使用的数据源
- `current_start`：当前使用的起始日期
- `min_rows_threshold`：可配置的行数阈值
- `incremental`：是否为增量模式（增量模式跳过预检查）

## 注意事项

1. **网络开销**：切换到akshare并从1970年开始抓取会增加网络请求时间
2. **存储空间**：完整历史数据会占用更多磁盘空间
3. **一次性切换**：每只股票在单次运行中最多切换一次数据源
4. **增量更新**：切换仅在初次抓取或非增量更新时触发

## 测试验证

运行测试脚本验证功能：

```bash
python test_auto_switch_datasource.py
```

测试覆盖：

- 文件不存在时的自动切换
- 文件存在但数据不足时的自动切换
- 文件存在且数据充足时的正常处理
- 增量模式下跳过预检查的行为
- 切换后的数据完整性验证

## 配置建议

### 不同场景的阈值设置

- **日内交易**：建议设置较低阈值（20-30行）
- **技术分析**：建议使用默认阈值（50行）
- **长期投资**：建议设置较高阈值（100-200行）
- **数据完整性优先**：建议设置较高阈值，确保获得充足历史数据

### 性能优化

- 对于已知数据充足的股票池，可以设置较高阈值避免不必要的检查
- 对于新股或小盘股较多的场景，建议使用默认或较低阈值
- 在网络条件较差时，可以适当提高阈值减少切换频率
