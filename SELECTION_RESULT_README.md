# 选股结果排序功能说明

## 功能概述

在原有的股票选择功能基础上，新增了选股结果排序和输出功能。所有选择器选出的股票会被汇总、去重，然后按照盈亏比进行排序，最终输出到 `selection_result/` 目录下的CSV文件中。

## 输出文件格式

### 文件位置
- 目录：`selection_result/`
- 文件名格式：`selection_result_YYYYMMDD.csv`
- 编码：UTF-8 with BOM

### 文件内容
CSV文件包含4列：

| 列名 | 说明 | 数据类型 | 示例 |
|------|------|----------|------|
| 股票名称 | 股票的中文名称 | 字符串 | 万向钱潮 |
| 股票代码 | 6位股票代码（含前导零） | 字符串 | 000559 |
| 盈亏比 | 计算得出的盈亏比值 | 数值 | 4.750 |
| 市值(亿元) | 股票市值，单位为亿元 | 数值 | 249.44 |

## 盈亏比计算公式

```
盈亏比 = (最近90天的最高价 - 当前价格) / (当前价格 - 最近60天最低价格)
```

### 计算说明
- **最近90天的最高价**：从交易日往前推90天内的最高价（使用high列）
- **当前价格**：交易日当天或之前最新的收盘价（使用close列）
- **最近60天最低价**：从交易日往前推60天内的最低价（使用low列）
- **特殊处理**：当分母为0或负数时，盈亏比设为0

## 排序规则

股票按照盈亏比**降序**排列，即盈亏比最高的股票排在最前面。

## 使用方法

### 运行选股程序
```bash
# 使用默认参数（所有股票，最新日期）
python select_stock.py

# 指定日期
python select_stock.py --date "2025-08-01"

# 指定股票范围
python select_stock.py --tickers "000001,600036,000002"
```

### 查看结果
选股完成后，会在控制台显示：
1. 各个选择器的选股结果
2. 汇总信息（总共选出多少只不重复股票）
3. 按盈亏比排序后的前10只股票信息

同时会生成CSV文件保存完整结果。

## 示例输出

### 控制台输出
```
============== 汇总选股结果 ==============
总共选出 38 只不重复股票
选股结果已保存到: selection_result\selection_result_20250730.csv
共选出 38 只股票
排序后前10只股票:
1. 万向钱潮(000559) - 盈亏比: 4.750, 市值: 249.44亿元
2. 爱慕股份(603511) - 盈亏比: 2.694, 市值: 61.42亿元
3. 上海港湾(605598) - 盈亏比: 1.764, 市值: 54.71亿元
...
```

### CSV文件内容
```csv
股票名称,股票代码,盈亏比,市值(亿元)
万向钱潮,000559,4.749999999999995,249.43624647
爱慕股份,603511,2.6944444444444438,61.41569302
上海港湾,605598,1.7640845070422542,54.71362177
...
```

## 数据来源

- **股票名称**：从最新的 `data/mktcap_*.csv` 文件中获取
- **市值数据**：从最新的 `data/mktcap_*.csv` 文件中获取，自动转换为亿元单位
- **价格数据**：从 `data/` 目录下的各股票CSV文件中获取

## 测试功能

可以使用测试脚本验证功能正确性：

```bash
python test_selection_result.py
```

测试内容包括：
- 文件格式检查
- 数据类型验证
- 排序正确性验证
- 数据完整性检查

## 注意事项

1. **股票代码格式**：CSV文件中股票代码保持6位格式（含前导零），但pandas读取时需要指定为字符串类型
2. **日期范围**：盈亏比计算需要足够的历史数据（至少90天）
3. **数据依赖**：需要确保 `data/` 目录下有相应的股票数据文件和市值数据文件
4. **去重处理**：多个选择器选出的相同股票会自动去重
5. **文件覆盖**：同一日期的结果文件会被覆盖

## 文件结构

```
selection_result/
├── selection_result_20250730.csv
├── selection_result_20250801.csv
└── ...
```

每次运行会根据交易日期生成对应的结果文件，便于历史数据的管理和对比。
