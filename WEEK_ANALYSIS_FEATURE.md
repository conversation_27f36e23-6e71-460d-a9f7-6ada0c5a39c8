# 一周表现分析功能说明

## 功能概述

在原有回测系统基础上，新增了一周内股价表现的深度分析功能，为投资决策提供更全面的参考信息。

## 新增分析指标

### 1. 一周内最高价分析
- **最高价格出现时间**: 统计股价在接下来一周内达到最高点的平均天数
- **最高价格收益率**: 计算从买入价到一周内最高价的收益率
- **平均最高价收益率**: 所有股票一周内最高价收益率的平均值

### 2. 一周内最低价分析  
- **最低价格出现时间**: 统计股价在接下来一周内达到最低点的平均天数
- **最低价格收益率**: 计算从买入价到一周内最低价的收益率（通常为负值）
- **平均最低价收益率**: 所有股票一周内最低价收益率的平均值

### 3. 日均表现分析
- **日均最高价收益率**: 一周内每日最高价相对买入价的平均收益率
- **日均最低价收益率**: 一周内每日最低价相对买入价的平均收益率

## 实际回测结果分析

基于2025年6月16日至7月16日的回测数据：

### 少妇战法一周表现
- **一周内最高价平均收益率**: 3.44%
- **最高价平均出现在第**: 2.6天
- **一周内最低价平均收益率**: -2.19%
- **最低价平均出现在第**: 2.4天

**分析**: 少妇战法选出的股票通常在第2-3天达到最高点，平均能获得3.44%的最大收益，但也存在平均2.19%的最大亏损风险。

### 补票战法一周表现
- **一周内最高价平均收益率**: 3.57%
- **最高价平均出现在第**: 2.3天
- **一周内最低价平均收益率**: -3.51%
- **最低价平均出现在第**: 2.8天

**分析**: 补票战法的股票波动较大，最高收益率略高于少妇战法，但最大亏损风险也更高。

### 填坑战法一周表现
- **一周内最高价平均收益率**: 5.35%
- **最高价平均出现在第**: 3.0天
- **一周内最低价平均收益率**: -2.05%
- **最低价平均出现在第**: 2.5天

**分析**: 填坑战法显示出最高的一周最大收益潜力（5.35%），且风险相对较低。

### TePu战法一周表现
- **一周内最高价平均收益率**: 5.27%
- **最高价平均出现在第**: 3.6天
- **一周内最低价平均收益率**: -1.66%
- **最低价平均出现在第**: 2.0天

**分析**: TePu战法的股票最高点出现较晚（第3.6天），但风险控制最好，最大亏损仅1.66%。

## 投资策略建议

### 短线操作建议
1. **2-3天止盈**: 大多数策略的最高点出现在第2-3天，建议在此时间窗口考虑止盈
2. **风险控制**: 所有策略都存在2-4%的最大亏损风险，建议设置相应止损位

### 策略选择建议
1. **追求高收益**: 填坑战法和TePu战法显示出更高的一周最大收益潜力
2. **风险控制**: TePu战法的最大亏损风险最小，适合保守投资者
3. **时机把握**: 补票战法的最高点出现最早（2.3天），适合快进快出

## 技术实现

### 数据结构
每个股票的一周分析包含以下信息：
```json
{
  "week_analysis": {
    "days_analyzed": 5,
    "max_high": {
      "day": 4,
      "date": "2025-06-20",
      "price": 4.39,
      "return_pct": 3.05
    },
    "min_low": {
      "day": 1,
      "date": "2025-06-17", 
      "price": 4.23,
      "return_pct": -0.70
    },
    "avg_high_return": 2.77,
    "avg_low_return": 0.61,
    "daily_data": [...]
  }
}
```

### 统计汇总
系统自动计算每个策略的一周表现统计：
- 最高价收益率分布
- 最低价收益率分布  
- 峰值和谷值出现时间分布
- 日均表现统计

## 使用方法

### 运行回测
```bash
uv run backtest.py --data-dir ./data --config ./configs.json --days 30 --output-dir ./backtest_result_week
```

### 生成报告
```bash
uv run backtest_report.py --result-dir ./backtest_result_week --output backtest_report_week.txt
```

## 文件输出

1. **JSON结果文件**: 包含每只股票的详细一周表现数据
2. **分析报告**: 包含一周表现分析的中文报告
3. **统计汇总**: 策略级别的一周表现统计

## 应用价值

1. **优化持仓时间**: 了解最佳卖出时机
2. **风险评估**: 量化最大可能亏损
3. **策略比较**: 多维度评估策略优劣
4. **投资决策**: 基于历史数据制定交易计划

这个功能为投资者提供了更精细的市场时机把握工具，有助于提高投资决策的科学性和有效性。
