# fetch_kline.py 改进功能说明

## 主要改进

### 1. 错误文件及时保存
- **问题**: 之前只在所有任务完成后才保存失败列表
- **改进**: 每次股票抓取失败时立即保存到 `failed_stocks.txt`
- **好处**: 即使程序中途中断，也能保留失败记录

### 2. 增量运行检查
- **问题**: 每次都重新下载所有数据
- **改进**: 检查已有数据的日期范围，只下载缺失的部分
- **好处**: 大幅减少重复下载，提高效率

### 3. 失败重试机制优化
- **问题**: 失败股票需要手动处理
- **改进**: 自动加载失败列表并优先处理，支持专门的重试模式
- **好处**: 自动化处理失败股票，减少手动干预

## 新增功能

### 1. 失败列表管理
```python
# 立即保存失败股票
save_failed_list([code], out_dir, "抓取失败原因")

# 加载失败列表
failed_codes = load_failed_list(out_dir)
```

### 2. 数据完整性检查
```python
# 检查已有数据是否覆盖所需日期范围
need_download, already_done = check_existing_data(codes, out_dir, start, end)
```

### 3. 新增命令行参数
```bash
# 只处理失败列表中的股票
python fetch_kline.py --retry-failed-only

# 设置数据质量检查阈值
python fetch_kline.py --max-null-ratio 0.2
```

## 使用方法

### 正常模式（增量更新）
```bash
# 第一次运行
python fetch_kline.py --datasource mootdx --start 20240101 --end 20241201

# 第二次运行（只下载新数据）
python fetch_kline.py --datasource mootdx --start 20240101 --end 20241201
```

### 失败重试模式
```bash
# 只处理失败的股票
python fetch_kline.py --retry-failed-only --datasource mootdx
```

### 数据质量控制
```bash
# 设置更严格的空值检查（默认30%）
python fetch_kline.py --max-null-ratio 0.1 --datasource mootdx
```

## 文件说明

### failed_stocks.txt
失败股票列表文件，格式如下：
```
# 抓取失败的股票列表 - 更新时间: 2024-12-01 15:30:00
# 失败原因: 第1轮抓取失败
# 总计 5 只股票
# 格式: 每行一个股票代码
#==================================================
000001
000002
600000
600001
600002
```

## 运行流程

### 正常模式流程
1. 市值筛选获取股票池
2. 检查已有数据，确定需要下载的股票
3. 加载失败列表，优先处理失败股票
4. 多线程下载，失败时立即保存到失败列表
5. 多轮重试机制
6. 清理成功股票从失败列表中移除

### 失败重试模式流程
1. 直接从失败列表加载股票
2. 跳过市值筛选
3. 执行下载和重试逻辑
4. 更新失败列表

## 日志输出示例

```
2024-12-01 15:30:00 [INFO] 检查已有数据，实现增量更新...
2024-12-01 15:30:01 [INFO] 数据检查完成: 需下载 150 只，已完成 2850 只
2024-12-01 15:30:01 [INFO] 发现之前失败的股票 5 只，将优先处理
2024-12-01 15:30:01 [INFO] 其中 3 只在当前股票池中，将优先处理
2024-12-01 15:30:02 [INFO] 开始抓取 150 支股票 (总计3000支，已完成2850支)
2024-12-01 15:30:05 [INFO] 失败股票列表已更新至: data/failed_stocks.txt (共2只)
2024-12-01 15:35:00 [INFO] 第 1 轮完成，2 只股票失败
2024-12-01 15:35:05 [INFO] 第 2 轮完成，所有股票处理成功
2024-12-01 15:35:05 [INFO] 所有股票处理成功，已删除失败列表文件
```

## 测试脚本

运行 `test_fetch_improvements.py` 来测试新功能：
```bash
python test_fetch_improvements.py
```

测试内容包括：
- 增量更新功能
- 失败列表管理
- 数据质量检查

## 注意事项

1. **失败列表文件**: 程序会自动维护 `data/failed_stocks.txt`，不建议手动编辑
2. **增量更新**: 基于CSV文件的日期范围判断，确保日期列格式正确
3. **数据质量**: 可通过 `--max-null-ratio` 参数调整质量检查严格程度
4. **重试机制**: 最多3轮重试，每轮间有递增的等待时间
5. **并发控制**: 通过 `--workers` 参数控制并发数，避免过度请求

## 性能优化

1. **跳过已完成**: 自动跳过日期范围已覆盖的股票
2. **优先失败**: 优先处理之前失败的股票
3. **及时保存**: 失败时立即保存，避免重复处理
4. **智能清理**: 成功后自动从失败列表移除
